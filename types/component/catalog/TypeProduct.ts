import { ProductStatus, Currency, ChargePolicy, DiscountType, DiscountLevel } from "@/lib/graphql/types/generated/graphql";

export interface ProductDimension {
    key: string;
    value: string;
}

// Extended DocumentInput to handle existing documents with URLs
export interface ExtendedDocumentInput {
    id?: string;
    file?: File | null;
    description?: string;
    tags?: string[];
    signedReadURL?: string;
    signedWriteURL?: string;
}

// Custom tag input interface for form handling
export interface ProductCustomTagInput {
    id?: string;
    label: string;
    key: string;
    value: string;
    type: string;
    description?: string;
}

export interface ProductFormData {
    productId?: string;
    name: string;
    productCode: string;
    description: string;
    status: ProductStatus;
    dimensions: ProductDimension[];
    pricing: {
        id?: string;
        chargePolicy: ChargePolicy;
        costPrice: {
            value: number;
            currency: Currency;
        };
        listPrice: {
            value: number;
            currency: Currency;
        };
        sellingPrice: {
            value: number;
            currency: Currency;
        };
        unit: {
            unit: number;
            unitType: string;
        };
        discounts: {
            discountType: DiscountType;
            discountLevel: DiscountLevel;
            discountValue: {
                amount: {
                    value: number;
                    currency: Currency;
                };
                percentage: number;
            };
        }[];
        margin: {
            absoluteAmount: {
                value: number;
                currency: Currency;
            };
            percentage: number;
        };
    };
    // Additional details fields
    documents: ExtendedDocumentInput[];
    customTags: ProductCustomTagInput[];
}

export interface ProductUpsertInput {
    id?: string;
    productCode: string;
    name: string;
    description: string;
    dimensions: ProductDimension[];
    pricing: {
        chargePolicy: ChargePolicy;
        costPrice: {
            value: number;
            currency: Currency;
        };
        listPrice: {
            value: number;
            currency: Currency;
        };
        sellingPrice: {
            value: number;
            currency: Currency;
        };
        productUnit: {
            unit: number;
            unitType: string;
        };
        discounts: {
            discountType: DiscountType;
            discountLevel: DiscountLevel;
            discountValue: {
                amount: {
                    value: number;
                    currency: Currency;
                };
                percentage: number;
            };
        }[];
        margin: {
            absoluteAmount: {
                value: number;
                currency: Currency;
            };
            percentage: number;
        };
    };
} 