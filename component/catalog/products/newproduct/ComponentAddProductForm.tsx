'use client';

import React, { useMemo, useEffect, useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm, FormProvider } from 'react-hook-form';
import AIInsightsPanel from '@/component/ai/ComponentAIInsights';
import { ProductFormData } from '@/types/component/catalog/TypeProduct';
import ComponentNote from '@/component/common/ComponentNote';
import ComponentProductInfoSection from '@/component/catalog/products/newproduct/ComponentProductInfoSection';
import ComponentProductAdditionalDetailsSection from '@/component/catalog/products/newproduct/ComponentProductAdditionalDetailsSection';
import { useMasterProductUpsertDetailsMutation, useGetProductByIdQuery } from '@/lib/graphql/types/generated/hooks';
import { ProductStatus, Currency, ChargePolicy, DiscountType, DiscountLevel } from '@/lib/graphql/types/generated/graphql';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';
import ComponentProductPricingSection from '@/component/catalog/products/newproduct/ComponentProductPricingSection';

const currencyOptions = [
    { value: 'USD', label: '$' },
    { value: 'INR', label: '₹' },
    { value: 'AED', label: 'د.إ' }
];

const defaultProduct: ProductFormData = {
    name: '',
    productCode: '',
    description: 'This is default description',
    status: ProductStatus.Active,
    dimensions: [],
    pricing: {
        chargePolicy: ChargePolicy.Unit,
        costPrice: {
            value: 0,
            currency: 'USD' as Currency
        },
        listPrice: {
            value: 0,
            currency: 'USD' as Currency
        },
        sellingPrice: {
            value: 0,
            currency: 'USD' as Currency
        },
        unit: {
            unit: 1,
            unitType: ''
        },
        discounts: [{
            discountType: DiscountType.Percentage,
            discountLevel: DiscountLevel.Product,
            discountValue: {
                amount: {
                    value: 0,
                    currency: 'USD' as Currency
                },
                percentage: 0
            }
        }],
        margin: {
            absoluteAmount: {
                value: 0,
                currency: 'USD' as Currency
            },
            percentage: 0
        }
    },
    documents: [],
    customTags: []
};

const computeSellingPrice = (listPrice: number, discount: number) => {
    const price = Number(listPrice) || 0;
    const disc = Number(discount) || 0;
    return Math.max(price - disc, 0);
};

const ComponentAddProductForm: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const isEditMode = searchParams.get('mode') === 'edit';
    const productId = searchParams.get('id');

    const [isLoading, setIsLoading] = useState(false);
    const [formNote, setFormNote] = useState<React.ReactNode | null>(null);

    const [masterProductUpsertDetails] = useMasterProductUpsertDetailsMutation();

    //todo: use the new query of GetProductById
    const { data: productData, refetch: refetchProductData } = useGetProductByIdQuery({
        variables: { id: productId || '' },
        skip: !isEditMode || !productId,
    });

    const methods = useForm<ProductFormData>({
        defaultValues: defaultProduct,
    });

    const { control, handleSubmit, register, setValue, watch } = methods;

    // Load existing product data when in edit mode
    useEffect(() => {
        if (isEditMode && productData?.masterProductGetById) {
            const product = productData.masterProductGetById;
            if (product) {
                methods.reset({
                    productId: product.id,
                    name: product.name,
                    productCode: product.productCode || '',
                    description: product.description || '',
                    status: product.status,
                    dimensions: product.dimensions?.map(d => ({
                        key: d.key,
                        value: d.value
                    })) || [],
                    pricing: {
                        id: product.pricing.id,
                        chargePolicy: product.pricing.chargePolicy,
                        costPrice: {
                            value: product.pricing.costPrice.value,
                            currency: product.pricing.costPrice.currency
                        },
                        listPrice: {
                            value: product.pricing.listPrice.value,
                            currency: product.pricing.listPrice.currency
                        },
                        sellingPrice: {
                            value: product.pricing.sellingPrice.value,
                            currency: product.pricing.sellingPrice.currency
                        },
                        unit: {
                            unit: product.pricing.unit.unit,
                            unitType: product.pricing.unit.unitType
                        },
                        discounts: product.pricing.discount && product.pricing.discount.length > 0 ? [{
                            discountLevel: DiscountLevel.Product,
                            discountType: product.pricing.discount[0]?.discountType,
                            discountValue: {
                                percentage: product.pricing.discount[0]?.discountValue.percentage,
                                amount: {
                                    value: product.pricing.discount[0]?.discountValue.value.value,
                                    currency: product.pricing.discount[0]?.discountValue.value.currency
                                }
                            }
                        }] : [],
                        margin: {
                            absoluteAmount: {
                                value: 0,
                                currency: product.pricing.costPrice.currency
                            },
                            percentage: 0
                        }
                    },
                    // Map existing documents to form format
                    documents: product.documents?.map(doc => ({
                        id: doc.id,
                        file: null, // Existing documents don't have File objects
                        description: '', // Document description not available in current schema
                        tags: [], // Document tags not available in current schema
                        signedReadURL: doc.file?.signedReadURL || undefined,
                        signedWriteURL: doc.file?.signedWriteURL || undefined
                    })) || [],
                    // Map existing custom tags to form format
                    customTags: product.customTags?.map(tag => ({
                        id: tag.id,
                        label: tag.label,
                        key: tag.key,
                        value: tag.value,
                        type: tag.type as string, // Convert enum to string for component
                        description: tag.description || ''
                    })) || []
                });
            }
        }
    }, [isEditMode, productData, methods, productId]);

    const handleSaveProduct = async (data: ProductFormData) => {
        setIsLoading(true);
        try {
            await masterProductUpsertDetails({
                variables: {
                    input: {
                        id: data.productId,
                        name: data.name,
                        productCode: data.productCode,
                        description: data.description,
                        dimensions: data.dimensions,
                        pricing: {
                            id: data.pricing.id,
                            chargePolicy: data.pricing.chargePolicy,
                            costPrice: data.pricing.costPrice,
                            listPrice: data.pricing.listPrice,
                            sellingPrice: data.pricing.sellingPrice,
                            productUnit: data.pricing.unit,
                            discounts: data.pricing.discounts,
                            margin: data.pricing.margin
                        }
                    }
                },
                onCompleted: async (data) => {
                    if (data.masterProductUpsertDetails) {
                        methods.setValue('productId', data.masterProductUpsertDetails.id);
                        methods.setValue('pricing.id', data.masterProductUpsertDetails.pricing.id);
                        setIsLoading(false);
                        setFormNote(<ComponentNote>Product details saved successfully. Additional Details are enabled!</ComponentNote>);
                        router.push(`/catalog/products/new?mode=edit&id=${data.masterProductUpsertDetails.id}`);
                        if (isEditMode && productId) {
                            await refetchProductData();
                        }
                    }
                },
                onError: (error) => {
                    setIsLoading(false);
                    setFormNote(<ComponentNote isError={true}>{getUserFriendlyErrorMessage(error as ApolloError)}</ComponentNote>);
                }
            });
        } finally {
            setIsLoading(false);
        }
    };

    const currency = watch('pricing.listPrice.currency') || 'USD';
    const sellingPrice = watch('pricing.sellingPrice.value') || 0;
    const unit = watch('pricing.unit.unit') || 1;
    const unitType = watch('pricing.unit.unitType') || '';
    const listPrice = watch('pricing.listPrice.value');
    const discount = watch('pricing.discounts.0.discountValue.amount.value');
    const name = watch('name') || '';
    const dimensions = watch('dimensions') || [];
    const marginPercentage = watch('pricing.margin.percentage');

    // Intuitive, visually effective display name builder for product + dimensions
    const displayName = useMemo(() => {
        const productName = name.trim();
        const hasDimensions = dimensions?.some(d => d.key && d.value);
        const validDimensions = dimensions?.filter((d: any) => d.key && d.value) || [];

        return (
            <div className="flex flex-wrap items-center gap-2 leading-relaxed">
                {/* Main product creation text with emphasis on user content */}
                <span className="text-gray-600 font-normal">Creating</span>

                {/* Product name - prominently displayed */}
                {productName ? (
                    <span className="font-bold text-gray-900 text-lg">{productName}</span>
                ) : (
                    <span className="font-semibold text-gray-400 italic text-lg">Untitled Product</span>
                )}

                {/* Dimensions section with natural flow */}
                {hasDimensions && (
                    <>
                        <span className="text-gray-600 font-normal">with </span>
                        <div className="flex flex-wrap gap-2 items-center">
                            {validDimensions.map((d: any, idx: number) => (
                                <span
                                    key={`${d.key}-${idx}`}
                                    className="inline-flex items-center px-2 py-1.5 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 hover:shadow-sm transition-all duration-200"
                                >
                                    <span className="text-gray-600 text-sm font-medium">{d.key}</span>
                                    <span className="mx-2 text-blue-400 font-medium">→</span>
                                    <span className="text-blue-600 font-bold text-sm">{d.value}</span>
                                </span>
                            ))}
                        </div>
                    </>
                )}
            </div>
        );
    }, [name, dimensions]);

    React.useEffect(() => {
        setValue('pricing.sellingPrice.value', computeSellingPrice(listPrice, discount));
    }, [listPrice, discount, setValue]);

    return (
        <div className="min-h-screen">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="mb-8 max-w-2xl mx-auto">
                    <AIInsightsPanel />
                </div>

                <div className="mb-8">
                    <button
                        onClick={() => router.back()}
                        className="mb-6 flex items-center text-gray-600 hover:text-secondary transition-colors"
                    >
                        <ArrowLeft className="h-5 w-5 mr-2" />
                        Back
                    </button>
                </div>

                <FormProvider {...methods}>
                    <form onSubmit={handleSubmit(handleSaveProduct)} className="space-y-8">
                        <div className='flex justify-between items-center'>
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900">Product & Pricing</h2>
                                <p className="text-sm text-gray-600">Add product details with pricing information</p>
                            </div>
                        </div>

                        {formNote && (
                            <div className="mb-6">
                                {formNote}
                            </div>
                        )}

                        <ComponentNote>
                            <div className="space-y-3">
                                {/* Product preview with improved typography */}
                                <div className="text-base">
                                    {displayName}
                                </div>

                                {/* Pricing information with clear hierarchy */}
                                <div className="flex flex-wrap items-center gap-4 text-md">
                                    <div className="flex items-center gap-1">
                                        <span className="text-gray-600 font-normal">Priced at</span>
                                        <span className="font-bold text-gray-900">
                                            {currencyOptions.find(c => c.value === currency)?.label}{listPrice || 0}
                                        </span>
                                        <span className="text-gray-600 font-normal">per</span>
                                        <span className="font-semibold text-gray-800">{unit} {unitType || 'unit'}</span>
                                    </div>

                                    <div className="flex items-center gap-1">
                                        <span className="text-gray-600 font-normal">Final price:</span>
                                        <span className="font-bold text-lg text-green-700">
                                            {currencyOptions.find(c => c.value === currency)?.label}{sellingPrice || 0}
                                        </span>
                                    </div>

                                    {marginPercentage !== undefined && (
                                        <div className="flex items-center gap-1">
                                            <span className="text-gray-600 font-normal">Margin:</span>
                                            <span className={`font-bold text-lg ${marginPercentage >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                                                {marginPercentage.toFixed(2)}%
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </ComponentNote>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 bg-white rounded-lg border border-gray-200 p-4">
                            <div className="rounded-lg p-6 flex flex-col space-y-6">
                                <ComponentProductInfoSection control={control} register={register} />
                            </div>
                            <div className="rounded-lg  p-6 flex flex-col space-y-6">
                                <ComponentProductPricingSection control={control} register={register} watch={watch} />
                            </div>
                            <div className="flex justify-end col-span-2 mx-5">
                                <button
                                    type="submit"
                                    disabled={isLoading}
                                    className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer disabled:opacity-50"
                                >
                                    {isLoading ? 'Saving...' : 'Save Product'}
                                </button>
                            </div>
                        </div>
                    </form>
                </FormProvider>

                {
                    !methods.getValues('productId') && (
                        <div className="mb-6">
                            <div className="mb-6 mt-10">
                                <h2 className="text-xl font-bold text-gray-900 mb-2">Additional Details</h2>
                                <p className="text-gray-600">Attach documents and add custom tags to enrich your product information.</p>
                            </div>
                            <div className="">
                                <ComponentNote isError={true}> Product with Pricing Details need to be saved first, before additional details can be added for the product!</ComponentNote>
                            </div>
                        </div>
                    )
                }
                {methods.getValues('productId') && (
                    <ComponentProductAdditionalDetailsSection
                        productId={methods.getValues('productId')!}
                        initialDocuments={methods.getValues('documents') || []}
                        initialCustomTags={methods.getValues('customTags') || []}
                        onSaveSuccess={async () => {
                            if (isEditMode && productId) {
                                await refetchProductData();
                            }
                        }}
                    />
                )}
            </div>
        </div>
    );
};

export default ComponentAddProductForm;