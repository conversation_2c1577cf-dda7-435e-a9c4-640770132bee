import React, { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import ComponentDocumentUpload, { DocumentInput } from '@/component/common/ComponentDocumentUpload';
import { ComponentCustomTagsInput } from '@/component/common/ComponentCustomTagInput';
import { SaveIcon } from 'lucide-react';
import { useMasterProductUpsertAdditionalDetailsMutation } from '@/lib/graphql/types/generated/hooks';
import {
    MasterProductUpsertAdditionalDetailsInput,
    CustomTagType
} from '@/lib/graphql/types/generated/graphql';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';
import ComponentNote from '@/component/common/ComponentNote';
import { ExtendedDocumentInput, ProductCustomTagInput } from '@/types/component/catalog/TypeProduct';

// Form data interface for additional details
interface ProductAdditionalDetailsFormData {
    documents: ExtendedDocumentInput[];
    customTags: ProductCustomTagInput[];
}

interface ComponentProductAdditionalDetailsSectionProps {
    productId: string;
    initialDocuments?: ExtendedDocumentInput[];
    initialCustomTags?: ProductCustomTagInput[];
    onSaveSuccess?: () => void;
}

const ComponentProductAdditionalDetailsSection: React.FC<ComponentProductAdditionalDetailsSectionProps> = ({
    productId,
    initialDocuments = [],
    initialCustomTags = [],
    onSaveSuccess
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [saveNote, setSaveNote] = useState<React.ReactNode | null>(null);

    const [masterProductUpsertAdditionalDetails] = useMasterProductUpsertAdditionalDetailsMutation();

    // Set up React Hook Form with initial data and validation
    const methods = useForm<ProductAdditionalDetailsFormData>({
        defaultValues: {
            documents: initialDocuments,
            customTags: initialCustomTags
        },
        mode: 'onChange' // Enable real-time validation
    });

    const { handleSubmit, watch, setValue } = methods;

    // Watch form values for real-time updates
    const documents = watch('documents');
    const customTags = watch('customTags');

    // Update form when initial data changes (for edit mode)
    useEffect(() => {
        methods.reset({
            documents: initialDocuments,
            customTags: initialCustomTags
        });
    }, [initialDocuments, initialCustomTags, methods]);

    const mapToMasterProductUpsertAdditionalDetailsInput = (data: ProductAdditionalDetailsFormData): MasterProductUpsertAdditionalDetailsInput => {
        // Convert component DocumentInput to GraphQL DocumentInput
        const graphqlDocuments = data.documents.map(doc => ({
            id: doc.id,
            file: doc.file ? undefined : (doc.signedReadURL || doc.signedWriteURL) ? {
                signedReadURL: doc.signedReadURL,
                signedWriteURL: doc.signedWriteURL
            } : undefined
        }));

        // Convert component CustomTagInput to GraphQL CustomTagInput
        const graphqlCustomTags = data.customTags.map(tag => ({
            id: tag.id || null, // Existing tags have IDs, new ones don't
            key: tag.key,
            label: tag.label,
            value: tag.value,
            type: tag.type as CustomTagType, // Convert string to enum
            description: tag.description
        }));

        return {
            masterProductId: productId,
            documents: graphqlDocuments,
            customTags: graphqlCustomTags
        };
    };

    const handleSaveAdditionalDetails = async (data: ProductAdditionalDetailsFormData) => {
        setIsLoading(true);
        setSaveNote(null);

        // Basic validation for custom tags
        const invalidTags = data.customTags.filter(tag =>
            tag.label.trim() === '' || tag.value.trim() === ''
        );

        if (invalidTags.length > 0) {
            setIsLoading(false);
            setSaveNote(<ComponentNote isError={true}>Please fill in all required fields for custom tags (label and value).</ComponentNote>);
            return;
        }

        try {
            await masterProductUpsertAdditionalDetails({
                variables: {
                    input: mapToMasterProductUpsertAdditionalDetailsInput(data)
                },
                onCompleted: async (data) => {
                    if (data.masterProductUpsertAdditionalDetails) {
                        setSaveNote(<ComponentNote>Additional details saved successfully!</ComponentNote>);

                        // Call success callback if provided
                        onSaveSuccess?.();
                    }
                },
                onError: (error) => {
                    setSaveNote(<ComponentNote isError={true}>{getUserFriendlyErrorMessage(error as ApolloError)}</ComponentNote>);
                }
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="mt-12">
            <div className="mb-4">
                <h2 className="text-2xl font-bold text-gray-900">Additional Details</h2>
                <p className="text-sm text-gray-600">Attach documents and add custom tags to enrich your product information.</p>
            </div>

            {saveNote && (
                <div className="mb-6">
                    {saveNote}
                </div>
            )}

            <FormProvider {...methods}>
                <form onSubmit={handleSubmit(handleSaveAdditionalDetails)} className="space-y-8">
                    <div className="bg-white rounded-lg border border-gray-200 p-6 flex flex-col space-y-8">
                        <ComponentDocumentUpload
                            value={documents as DocumentInput[]}
                            onDocumentsChange={(docs) => setValue('documents', docs as ExtendedDocumentInput[])}
                        />
                        <ComponentCustomTagsInput
                            value={customTags as any}
                            onTagsChange={(tags) => setValue('customTags', tags as ProductCustomTagInput[])}
                        />
                        <div className="flex justify-end col-span-2 mx-5">
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <div className='flex items-center gap-2'>
                                    <SaveIcon className='h-4 w-4' />
                                    {isLoading ? 'Saving...' : 'Save Additional Details'}
                                </div>
                            </button>
                        </div>
                    </div>
                </form>
            </FormProvider>
        </div >
    );
};

export default ComponentProductAdditionalDetailsSection;