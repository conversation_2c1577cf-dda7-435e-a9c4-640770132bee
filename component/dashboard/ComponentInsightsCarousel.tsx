import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Users, FileText, DollarSign, TrendingUp, Calculator, Receipt } from 'lucide-react';

interface InsightCard {
    id: string;
    title: string;
    category: 'customers' | 'quotes' | 'accounting';
    value: string | number;
    subtitle: string;
    trend?: {
        direction: 'up' | 'down' | 'neutral';
        value: string;
    };
    icon: React.ReactNode;
    color: string;
}

const insightCards: InsightCard[] = [
    // Customer Insights
    {
        id: 'total-customers',
        title: 'Total Customers',
        category: 'customers',
        value: 24,
        subtitle: 'Active customers',
        trend: { direction: 'up', value: '+3 this month' },
        icon: <Users className="w-5 h-5" />,
        color: 'bg-blue-500'
    },
    {
        id: 'new-customers',
        title: 'New This Month',
        category: 'customers',
        value: 3,
        subtitle: 'New customers',
        trend: { direction: 'up', value: '+50% vs last month' },
        icon: <Users className="w-5 h-5" />,
        color: 'bg-green-500'
    },
    
    // Quote Insights
    {
        id: 'pending-quotes',
        title: 'Pending Quotes',
        category: 'quotes',
        value: 8,
        subtitle: 'Awaiting approval',
        trend: { direction: 'neutral', value: '2 expiring soon' },
        icon: <FileText className="w-5 h-5" />,
        color: 'bg-orange-500'
    },
    {
        id: 'quote-value',
        title: 'Quote Value',
        category: 'quotes',
        value: '$89,850',
        subtitle: 'Total pending value',
        trend: { direction: 'up', value: '+15% this week' },
        icon: <Calculator className="w-5 h-5" />,
        color: 'bg-purple-500'
    },
    
    // Accounting Insights
    {
        id: 'monthly-revenue',
        title: 'Monthly Revenue',
        category: 'accounting',
        value: '$12,500',
        subtitle: 'Current month',
        trend: { direction: 'up', value: '+8% vs last month' },
        icon: <DollarSign className="w-5 h-5" />,
        color: 'bg-primary'
    },
    {
        id: 'outstanding-invoices',
        title: 'Outstanding',
        category: 'accounting',
        value: '$4,200',
        subtitle: 'Pending payments',
        trend: { direction: 'down', value: '-12% this week' },
        icon: <Receipt className="w-5 h-5" />,
        color: 'bg-red-500'
    }
];

const ComponentInsightsCarousel: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const cardsPerView = 2; // Show 2 cards at a time
    const maxIndex = Math.max(0, insightCards.length - cardsPerView);

    const nextSlide = () => {
        setCurrentIndex(prev => Math.min(prev + 1, maxIndex));
    };

    const prevSlide = () => {
        setCurrentIndex(prev => Math.max(prev - 1, 0));
    };

    const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
        switch (direction) {
            case 'up':
                return <TrendingUp className="w-3 h-3 text-green-600" />;
            case 'down':
                return <TrendingUp className="w-3 h-3 text-red-600 rotate-180" />;
            default:
                return null;
        }
    };

    const getTrendColor = (direction: 'up' | 'down' | 'neutral') => {
        switch (direction) {
            case 'up':
                return 'text-green-600';
            case 'down':
                return 'text-red-600';
            default:
                return 'text-gray-500';
        }
    };

    return (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 h-full">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h3 className="text-lg font-semibold text-gray-900">Business Insights</h3>
                    <p className="text-sm text-gray-500">Key metrics at a glance</p>
                </div>
                <div className="flex space-x-1">
                    <button
                        onClick={prevSlide}
                        disabled={currentIndex === 0}
                        className="p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-sm"
                    >
                        <ChevronLeft className="w-4 h-4 text-gray-600" />
                    </button>
                    <button
                        onClick={nextSlide}
                        disabled={currentIndex >= maxIndex}
                        className="p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-sm"
                    >
                        <ChevronRight className="w-4 h-4 text-gray-600" />
                    </button>
                </div>
            </div>

            {/* Carousel */}
            <div className="overflow-hidden">
                <div 
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{ transform: `translateX(-${currentIndex * (100 / cardsPerView)}%)` }}
                >
                    {insightCards.map((card) => (
                        <div key={card.id} className="w-1/2 flex-shrink-0 px-1">
                            <div className="bg-gray-50 rounded-lg p-4 h-full border border-gray-100 hover:shadow-md hover:bg-white transition-all duration-300 cursor-pointer group">
                                <div className="flex items-start justify-between mb-3">
                                    <div className={`p-2 rounded-lg ${card.color} text-white group-hover:scale-105 transition-transform duration-300 shadow-sm`}>
                                        {card.icon}
                                    </div>
                                    {card.trend && (
                                        <div className="flex items-center space-x-1">
                                            {getTrendIcon(card.trend.direction)}
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-1">
                                    <h4 className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                                        {card.title}
                                    </h4>
                                    <p className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                                        {card.value}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {card.subtitle}
                                    </p>
                                    {card.trend && (
                                        <p className={`text-xs font-medium ${getTrendColor(card.trend.direction)}`}>
                                            {card.trend.value}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Dots Indicator */}
            <div className="flex justify-center space-x-1 mt-4">
                {Array.from({ length: maxIndex + 1 }).map((_, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentIndex(index)}
                        className={`w-2 h-2 rounded-full transition-all duration-200 ${
                            index === currentIndex ? 'bg-primary' : 'bg-gray-300'
                        }`}
                    />
                ))}
            </div>
        </div>
    );
};

export default ComponentInsightsCarousel;
