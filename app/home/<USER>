'use client';

import React, { useEffect } from 'react';
import ComponentAIChatInterface from '@/component/ai/ComponentAIChatInterface';
import ComponentInsightsCarousel from '@/component/dashboard/ComponentInsightsCarousel';
import { useCompanyContext } from '@/component/auth/ComponentRouteProtection';

export default function HomePage() {

    const { hasCompany, retry } = useCompanyContext();

    useEffect(() => {
        if (!hasCompany) {
            retry();
        }
    }, [hasCompany, retry]);

    return (
        <div className="h-screen flex flex-col lg:flex-row bg-background overflow-hidden">
            {/* AI Chat Interface - 2/3 of screen on desktop, full height on mobile */}
            <div className="flex-1 lg:w-2/3 flex flex-col min-h-0">
                <ComponentAIChatInterface className="h-full" />
            </div>

            {/* Insights Sidebar - 1/3 of screen on desktop, bottom section on mobile */}
            <div className="lg:w-1/3 bg-gray-50 border-l border-gray-200 flex-shrink-0">
                <div className="h-full p-4 lg:p-6 overflow-hidden">
                    <ComponentInsightsCarousel />
                </div>
            </div>
        </div>
    );
}
